{"name": "vite-vue-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build --base=/spaceoptimization/", "preview": "vite preview"}, "dependencies": {"@azure/msal-browser": "^4.13.0", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "@primeuix/themes": "^1.2.3", "@primevue/themes": "^4.3.9", "@vueform/multiselect": "^2.6.11", "apexcharts": "^4.7.0", "axios": "^1.10.0", "chart.js": "^4.5.0", "chartjs-plugin-annotation": "^3.1.0", "chartjs-plugin-datalabels": "^2.2.0", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "echarts": "^6.0.0", "lucide-vue-next": "^0.513.0", "pinia": "^3.0.3", "plotly.js-dist-min": "^3.0.1", "primeicons": "^7.0.0", "primevue": "^4.3.9", "vue": "^3.4.38", "vue-chartjs": "^5.3.2", "vue-multiselect": "^3.2.0", "vue-router": "^4.5.1", "vue3-apexcharts": "^1.8.0", "vue3-datepicker": "^0.4.0", "vuetify": "^3.0.0"}, "devDependencies": {"@iconify/tailwind": "^1.2.0", "@vitejs/plugin-vue": "^5.1.3", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "tailwindcss-primeui": "^0.6.1", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.1.4"}}