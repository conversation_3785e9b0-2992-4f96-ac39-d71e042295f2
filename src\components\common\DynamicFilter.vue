<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

interface FilterOption {
  value: string | number
  label: string
  disabled?: boolean
  group?: string
}

interface Props {
  modelValue?: string | number | (string | number)[] | null
  multiselect?: boolean
  label: string
  placeholder?: string
  options: Array<string | FilterOption | Record<string, any>>

  // New props for flexible key mapping
  valueKey?: string
  labelKey?: string
  disabledKey?: string
  groupKey?: string

  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  error?: boolean
  disabled?: boolean

  searchable?: boolean
  clearable?: boolean
  closeOnSelect?: boolean
  maxHeight?: string
  maxSelections?: number

  containerClass?: string
  dropdownClass?: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  multiselect: false,
  modelValue: null,
  placeholder: '',
  valueKey: 'value',
  labelKey: 'label',
  disabledKey: 'disabled',
  groupKey: 'group',
  variant: 'primary',
  size: 'md',
  searchable: true,
  clearable: true,
  closeOnSelect: false,
  maxHeight: '200px',
  loading: false
})

const emits = defineEmits<{
  'update:modelValue': [value: any]
  change: [value: any]
  search: [query: string]
  open: []
  close: []
}>()

const selectedOptions = ref<FilterOption[]>([])
const selectedOption = ref<FilterOption | null>(null)
const isDropdownOpen = ref(false)
const searchQuery = ref('')
const dropdownRef = ref<HTMLElement>()

// Enhanced normalize options with flexible key mapping
const normalizedOptions = computed((): FilterOption[] => {
  return props.options.map(option => {
    if (typeof option === 'string') {
      return { value: option, label: option }
    }
    
    // Handle objects with flexible key mapping
    if (typeof option === 'object' && option !== null) {
      // Check if it's already in the correct format
      if ('value' in option && 'label' in option) {
        return { 
          value: option.value ?? option.label,
          label: option.label,
          disabled: option.disabled,
          group: option.group
        }
      }
      
      // Use flexible key mapping
      const value = option[props.valueKey] ?? option[props.labelKey] ?? Object.values(option)[0]
      const label = option[props.labelKey] ?? option[props.valueKey] ?? String(value)
      const disabled = option[props.disabledKey] ?? false
      const group = option[props.groupKey] ?? undefined
      
      return { 
        value, 
        label: String(label), 
        disabled: Boolean(disabled),
        group: group ? String(group) : undefined
      }
    }
    
    return { value: option, label: String(option) }
  })
})

// Watch for external modelValue changes
watch(
  () => props.modelValue,
  (newVal) => {
    if (props.multiselect) {
      if (Array.isArray(newVal)) {
        selectedOptions.value = normalizedOptions.value.filter(opt =>
          newVal.includes(opt.value)
        )
      } else {
        selectedOptions.value = []
      }
    } else {
      if (newVal !== null && newVal !== undefined) {
        selectedOption.value =
          normalizedOptions.value.find(opt => opt.value === newVal) || null
      } else {
        selectedOption.value = null
      }
    }
  },
  { immediate: true }
)

// Filter options based on search
const filteredOptions = computed(() => {
  if (!props.searchable || !searchQuery.value.trim()) {
    return normalizedOptions.value
  }
  const query = searchQuery.value.toLowerCase()
  return normalizedOptions.value.filter(option =>
    option.label.toLowerCase().includes(query) ||
    String(option.value).toLowerCase().includes(query)
  )
})

// Add "All" for multiselect
const optionsWithAll = computed(() => {
  if (props.multiselect && props.maxSelections == null && filteredOptions.value.length > 0) {
    return [{ value: '__all__', label: 'All' }, ...filteredOptions.value]
  }
  return filteredOptions.value
})

const isAllSelected = computed(() => {
  if (!props.multiselect) return false
  const availableOptions = filteredOptions.value.filter(opt => !opt.disabled)
  return (
    availableOptions.length > 0 &&
    availableOptions.every(option => isSelected(option))
  )
})

// Check if option is selected
const isSelected = (option: FilterOption): boolean => {
  if (props.multiselect) {
    return selectedOptions.value.some(
      selected => selected.value === option.value
    )
  }
  return selectedOption.value?.value === option.value
}

// Select option logic
const selectOption = (option: FilterOption) => {
  if (props.disabled) return
  if (option.disabled) return

  if (props.multiselect) {
    if (option.value === '__all__') {
      if (isAllSelected.value) {
        selectedOptions.value = []
      } else {
        selectedOptions.value = filteredOptions.value.filter(
          opt => !opt.disabled
        )
      }
    } else {
      const exists = isSelected(option)
      if (exists) {
        selectedOptions.value = selectedOptions.value.filter(
          opt => opt.value !== option.value
        )
      } else {
        if (
          !props.maxSelections ||
          selectedOptions.value.length < props.maxSelections
        ) {
          selectedOptions.value.push(option)
        }
      }
    }
    const newValues = selectedOptions.value.map(opt => opt.value)
    emits('update:modelValue', newValues)
    emits('change', newValues)
  } else {
    selectedOption.value = option
    emits('update:modelValue', option.value)
    emits('change', option.value)
    if (props.closeOnSelect !== false) closeDropdown()
  }
}

// Clear selections
const clearSelection = () => {
  if (props.multiselect) {
    selectedOptions.value = []
    emits('update:modelValue', [])
    emits('change', [])
  } else {
    selectedOption.value = null
    emits('update:modelValue', null)
    emits('change', null)
  }
}

// Dropdown control
const openDropdown = () => {
  if (props.disabled) return
  isDropdownOpen.value = true
  emits('open')
}

const closeDropdown = () => {
  isDropdownOpen.value = false
  searchQuery.value = ''
  emits('close')
}

const displayText = computed(() => {
  if (props.multiselect) {
    if (selectedOptions.value.length === 0) {
      return props.placeholder || props.label
    }
    if (selectedOptions.value.length === 1) {
      return selectedOptions.value[0].label
    }
    if (selectedOptions.value.length <= 2) {
      return selectedOptions.value.map(opt => opt.label).join(', ')
    }
    return `${selectedOptions.value.length} ${props.label.toLowerCase()} selected`
  }
  return selectedOption.value?.label || props.placeholder || props.label
})

// Classes
const buttonClasses = computed(() => {
  const base =
    'w-full flex items-center justify-between gap-2 px-3 py-2 text-left border rounded-lg font-medium transition-all duration-200'
  const sizeClasses = {
    sm: 'text-sm px-2 py-1.5',
    md: 'text-sm px-3 py-2',
    lg: 'text-base px-4 py-3'
  }
  const variantClasses = {
    primary: isDropdownOpen.value
      ? 'bg-white border-secondary shadow-md'
      : 'bg-gray-50 border-gray-300 hover:bg-gray-100',
    secondary: isDropdownOpen.value
      ? 'bg-white border-gray-400 shadow-md'
      : 'bg-white border-gray-300 hover:border-gray-400',
    outline: isDropdownOpen.value
      ? 'bg-white border-secondary shadow-md'
      : 'bg-white border-gray-300 hover:bg-gray-100',
    ghost: isDropdownOpen.value
      ? 'bg-gray-50 shadow-md'
      : 'bg-white hover:bg-gray-50'
  }
  const errorClasses = props.error
    ? 'border-red-500 bg-red-50 text-red-900'
    : ''
  const disabledClasses = props.disabled
    ? 'opacity-50 cursor-not-allowed'
    : 'cursor-pointer'

  return [
    base,
    sizeClasses[props.size],
    variantClasses[props.variant],
    errorClasses,
    disabledClasses
  ].join(' ')
})

watch(searchQuery, (newQuery) => {
  if (props.searchable) {
    emits('search', newQuery)
  }
})

const handleClickOutside = (event: Event) => {
  if (!isDropdownOpen.value) return

  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    closeDropdown()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside,true)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside,true)
})
</script>

<template>

  <div ref="dropdownRef" class="relative" :class="containerClass">
    <button
      type="button"
      :class="buttonClasses"
      :disabled="disabled"
      @click="isDropdownOpen ? closeDropdown() : openDropdown()"
    >
      <span :class="selectedOptions.length===0 && selectedOption=== null ?'text-gray-300':'text-gray-700'" class="flex-1 truncate text-left">{{ displayText }} </span>
      <div class="flex items-center gap-2 shrink-0">
        <div v-if="loading" class="animate-spin h-4 w-4 border-2 border-gray-300 rounded-full"></div>
        <button
          v-if="clearable && ((multiselect && selectedOptions.length > 0) || (!multiselect && selectedOption))"
          type="button"
          class="p-0.5 hover:bg-gray-200 rounded transition-colors"
          :disabled="disabled"
          :class="disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-200'"
          @click.stop="() => { if (!disabled) clearSelection() }"
        >
          <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
        <svg 
          class="w-4 h-4 text-gray-500 transition-transform duration-200"
          :class="{ 'rotate-180': isDropdownOpen }"
          fill="none" stroke="currentColor" viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </div>
    </button>

    <Transition
      enter-active-class="transition duration-200 ease-out"
      enter-from-class="transform scale-95 opacity-0"
      enter-to-class="transform scale-100 opacity-100"
      leave-active-class="transition duration-150 ease-in"
      leave-from-class="transform scale-100 opacity-100"
      leave-to-class="transform scale-95 opacity-0"
    >
      <div
        v-if="isDropdownOpen"
        class="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg"
        :class="dropdownClass"
      >
        <div v-if="searchable" class="p-3 border-b border-gray-100">
          <div class="relative">
            <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <input
              v-model="searchQuery"
              type="text"
              class="w-full pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-md focus:ring-2 focus:ring-tx-primary focus:border-transparent outline-none"
              placeholder="Search options..."
              @click.stop
            >
          </div>
        </div>

        <div class="overflow-y-auto" :style="{ maxHeight: maxHeight }">
          <div v-if="optionsWithAll.length === 0" class="px-4 py-8 text-center text-gray-300 text-sm">
            No options found
          </div>
          <div v-else class="py-1">
            <button
              v-for="(option, index) in optionsWithAll"
              :key="index"
              type="button"
              class="w-full flex items-center gap-3 px-4 py-2 text-left text-sm hover:bg-gray-50 transition-colors"
              :class="{
                'bg-green-50 text-tertiary': option.value === '__all__' ? isAllSelected : isSelected(option),
                'opacity-50 cursor-not-allowed': option.disabled || disabled,
                'cursor-pointer': !option.disabled && !disabled
               }"
              :disabled="option.disabled || disabled"
              @click="() => { if (!disabled && !option.disabled) selectOption(option) }"
            >
              <div v-if="multiselect" class="flex items-center">
                <input
                  type="checkbox"
                  :checked="option.value === '__all__' ? isAllSelected : isSelected(option)"
                  :disabled="option.disabled || disabled"
                  class="w-4 h-4 text-tx-primary border-gray-300 rounded focus:ring-tx-tertiary pointer-events-none"
                />
              </div>
              <div v-else class="flex items-center">
                <div
                  class="w-4 h-4 border-2 border-gray-300 rounded-full flex items-center justify-center text-tx-primary"
                  :class="{ 'border-tertiary bg-tertiary': isSelected(option) }"
                >
                  <div v-if="isSelected(option)" class="w-2 h-2 bg-white rounded-full"></div>
                </div>
              </div>
              <span class="flex-1">{{ option.label }}</span>
              <span v-if="option.group" class="text-xs text-gray-300 bg-gray-100 px-2 py-0.5 rounded">
                {{ option.group }}
              </span>
            </button>
          </div>
        </div>

        <div v-if="multiselect && selectedOptions.length > 0" class="px-4 py-2 bg-gray-50 border-t border-gray-100 text-xs text-gray-600">
          {{ selectedOptions.length }} selected
          <span v-if="maxSelections"> (max {{ maxSelections }})</span>
        </div>
      </div>
    </Transition>
  </div>
</template>

<style scoped>
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}
.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}
.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}
.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>