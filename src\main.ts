import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router'
import axios from 'axios';
import { createPinia } from 'pinia'
import PrimeVue from 'primevue/config'
import Aura from '@primevue/themes/aura'

const app = createApp(App);
const pinia = createPinia()
app.use(PrimeVue, {
  theme: {
    preset: Aura
  }
})
app.use(pinia)

export const baseURL = import.meta.env.VITE_BASE_URL;

export const baseFastapiURL = import.meta.env.VITE_FASTAPI_URL;

export const redirectUri = import.meta.env.VITE_AD_REDIRECT_URI;

axios.defaults.baseURL = baseURL;

app.provide("baseImageUrl", import.meta.env.VITE_BASE_PATH + "/assets");

app.config.globalProperties.baseImgUrl = import.meta.env.VITE_BASE_PATH + "/assets";

app.use(router);

app.mount("#app");

axios.interceptors.request.use(
  (config) => {
    const isLoginEndpoint = config.url && config.url.includes('/api/v1/auth/login/')
    
    if (!isLoginEndpoint) {
      const token = localStorage.getItem('access_token')
      
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
    }
    
    return config
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error)
  }
)
